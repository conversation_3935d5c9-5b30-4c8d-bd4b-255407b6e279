package com.paic.ncbs.claim.service.sop.impl;

import com.paic.ncbs.claim.common.util.RapeCollectionUtils;
import com.paic.ncbs.claim.common.util.RapeStringUtils;
import com.paic.ncbs.claim.model.vo.sop.SopMainVO;
import com.paic.ncbs.claim.service.sop.SopMainService;
import com.paic.ncbs.claim.service.sop.SopMatchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * SOP匹配服务实现类
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Slf4j
@Service
public class SopMatchServiceImpl implements SopMatchService {

    @Autowired
    private SopMainService sopMainService;

    @Override
    public List<SopMainVO> matchSopByCase(String reportNo, Integer caseTimes, String taskBpmKey) {
        log.info("根据案件信息匹配SOP规则，reportNo：{}，caseTimes：{}，taskBpmKey：{}",
                reportNo, caseTimes, taskBpmKey);

        if (RapeStringUtils.isEmptyStr(reportNo)) {
            log.warn("报案号为空，无法匹配SOP");
            return new ArrayList<>();
        }

        try {
            List<SopMainVO> matchedSops = sopMainService.matchSopRulesByCase(reportNo, caseTimes, taskBpmKey);

            log.info("匹配到{}条SOP规则", matchedSops.size());
            return matchedSops;

        } catch (Exception e) {
            log.error("根据案件信息匹配SOP规则失败", e);
            return new ArrayList<>();
        }
    }

}
