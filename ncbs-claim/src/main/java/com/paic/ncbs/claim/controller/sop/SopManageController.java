package com.paic.ncbs.claim.controller.sop;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.sop.SopMainDTO;
import com.paic.ncbs.claim.model.vo.sop.SopMainVO;
import com.paic.ncbs.claim.model.vo.sop.SopQueryVO;
import com.paic.ncbs.claim.service.sop.SopMainService;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * SOP管理Controller
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Api(tags = "SOP管理")
@RestController
@RequestMapping("/mng/app/sopManageAction")
@Slf4j
public class SopManageController extends BaseController {

    @Autowired
    private SopMainService sopMainService;

    @ApiOperation("分页查询SOP列表")
    @PostMapping(value = "/getSopList")
    public ResponseResult<List<SopMainVO>> getSopList(@RequestBody SopQueryVO queryVO) {
        try {
            LogUtil.audit("分页查询SOP列表，查询条件：{}", queryVO);
            List<SopMainVO> result = sopMainService.getSopList(queryVO);
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("分页查询SOP列表失败", e);
           throw new GlobalBusinessException(e.getMessage());
        }
    }

    @ApiOperation("获取SOP详情")
    @GetMapping(value = "/getSopDetail/{idSopMain}")
    @ApiImplicitParam(name = "idSopMain", value = "SOP主键", required = true, dataType = "String", paramType = "path")
    public ResponseResult<SopMainVO> getSopDetail(@PathVariable("idSopMain") String idSopMain) {
        try {
            LogUtil.audit("获取SOP详情，idSopMain：{}", idSopMain);
            SopMainVO result = sopMainService.getSopDetail(idSopMain);
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("获取SOP详情失败", e);
           throw new GlobalBusinessException(e.getMessage());
        }
    }

    @ApiOperation("保存或更新SOP")
    @PostMapping(value = "/saveOrUpdateSop")
    @ResponseBody
    public ResponseResult<String> saveOrUpdateSop(SopMainDTO sopMainDTO, HttpServletRequest request) {
        try {
            LogUtil.audit("保存或更新SOP，SOP名称：{}，操作类型：{}", sopMainDTO.getSopName(), sopMainDTO.getInitFlag());
            String result = sopMainService.saveOrUpdateSop(sopMainDTO, request);
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("保存或更新SOP失败", e);
           throw new GlobalBusinessException(e.getMessage());
        }
    }

    @ApiOperation("校验SOP名称是否重复")
    @GetMapping(value = "/checkSopNameExists")
    @ApiImplicitParam(name = "sopName", value = "SOP名称", required = true, dataType = "String", paramType = "query")
    public ResponseResult<Boolean> checkSopNameExists(@RequestParam("sopName") String sopName) {
        try {
            LogUtil.audit("校验SOP名称是否重复，sopName：{}", sopName);
            boolean result = sopMainService.checkSopNameExists(sopName);
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("校验SOP名称是否重复失败", e);
           throw new GlobalBusinessException(e.getMessage());
        }
    }

    @ApiOperation("获取险种信息列表")
    @GetMapping(value = "/getPlanInfoList")
    public ResponseResult<List<Object>> getPlanInfoList() {
        try {
            LogUtil.audit("获取险种信息列表");
            List<Object> result = sopMainService.getPlanInfoList();
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("获取险种信息列表失败", e);
           throw new GlobalBusinessException(e.getMessage());
        }
    }

}
