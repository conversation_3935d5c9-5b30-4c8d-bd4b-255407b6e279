package com.paic.ncbs.claim.service.taskdeal;

import com.github.pagehelper.PageInfo;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.base.exception.NcbsException;
import com.paic.ncbs.claim.model.vo.report.DepartmentVO;
import com.paic.ncbs.claim.model.vo.taskdeal.TaskInfoVO;
import com.paic.ncbs.claim.model.vo.taskdeal.UserWithTaskCountVO;
import com.paic.ncbs.um.model.dto.UserInfoDTO;

import java.util.List;

public interface TaskPoolService {

    PageInfo<TaskInfoVO> getNotDealTaskList(TaskInfoVO vo) throws GlobalBusinessException;

    Boolean isExistsVerifyRecord(TaskInfoVO vo) throws GlobalBusinessException;

    List<UserInfoDTO> searchTaskDealUser(String departmentCode, String key) throws GlobalBusinessException, NcbsException;

    List<UserWithTaskCountVO> getTaskDealUser(String key, String departmentCode, String userCode) throws NcbsException;

    void dispatchTask(List<String> taskIdList, String assignee,String assigneeName,String departmentCode) throws GlobalBusinessException;

    void reAssign(String id, String userId, String assigneeName,String comCode) throws GlobalBusinessException;

    DepartmentVO getSelectDepartmentList4Query(String departmentCode);

    List<DepartmentVO> getSelectDepartmentList4Dispatch(String departmentCode);

    List<DepartmentVO> getSelectDepartmentList();

    void addNoticesList(List<String> taskIdList, String newUserId);
}
