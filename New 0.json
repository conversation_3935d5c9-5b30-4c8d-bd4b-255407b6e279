======测试用例======
代码编写要求:
1.只用一个类写接口单元测试用例，要求覆盖ReplevyController类中的saveOrSubmitReplevy、saveReplevyDetail、saveOrSubmitReplevyFee、sendCashFlowSearch、initReplevy、initReplevyApprove接口方法。
2.接口Vo类中除主键外，所有字段都赋值
3.字段长度不能过长、字段类型不能与数据库不匹配。

======功能代码要求======
代码编写要求:
1.按现有代码结合上下文用编写java代码以及xml文件。
2.尽量复用现有工具类、日志记录类代码减少代码冗余。
3.list判断是否为空用CollectionUtils.isEmpty。

4.Mapper类实现sql脚本不要用baseDao.java中的方法，需重新在所关联的xml中写代码，insert和update类中需要映射实体类除主键外全字段。

======功能代码实现======
实现功能代码：
1.按现有代码结合上下文用java写一个输入数据库表名逆向生成Dao类,Vo类,impl,Map.xml文件的方法。
2.要求使用现有dev数据库连接配置。

代码编写要求:
1.按现有代码结合上下文用编写java代码以及xml文件。
2.尽量复用现有工具类、日志记录类代码减少代码冗余。

4.list判断是否为空用CollectionUtils.isEmpty。
实现功能代码：
1.在saveOrSubmitReplevy方法中将replevySuggestVoList、replevyDetailList记录数据库

代码编写要求:
1.按现有代码结合上下文用编写java代码以及xml文件。
2.尽量复用现有工具类、日志记录类代码减少代码冗余。
3.list判断是否为空用CollectionUtils.isEmpty。
实现功能代码：
1.在ReplevyServiceImpl类中的queryPlanAndDutyInfo方法写一个查询方法
2.要求用报案号，和initFlag作为查询条件区分现有的查询，sql查询为:
SELECT  
DUTY_CODE ,
duty_NAME ,
PLAN_CODE
FROM CLMS_ESTIMATE_DUTY_RECORD pp,clm_case_base cb
where pp.CASE_NO = cb.CASE_NO and cb.REPORT_NO  = ''
group by PLAN_CODE ;

在ReplevyServiceImpl类的saveReplevyDetail方法中加入一个计算更新总追偿收入金额的方法
1.需要通过replevyApiVo中的replevyChargeId字段查询sql得到原有总金额：
select SUM(l.replevied_money) from clms_replevy_detail d,clms_replevy_loss l
where d.replevy_no = l.replevy_no and d.id = #{replevyChargeId} group by l.replevy_no;
2.再加更新到中clms_Replevy_Main表的sum_real_replevy中，通过replevyApiVo 的 ReplevyId作为主键ID。


ReplevyControllerTest中创建ClmsReplevyChargeVo,SendReplevyVo,ClmsReplevyTextVo,PayReplevyVo对象初始化时set了不存在的字段,需要重新初始化对象。

ReplevyControllerTest中补全追偿收入信息private List<ClmsReplevyLossVo> replevyLossVoList对象初始化,注意字段值字段长度不能过长、字段类型不能与数据库不匹配。

1.ReplevyControllerTest的方法setUp中补全PolicyNo、ReportNo、ReplevyNo、ClaimNo静态号码，要求以大写英文字母开头随机生成10位。
2.ReplevyControllerTest中所有setPolicyNo、setReportNo、setReplevyNo、setClaimNo全部使用setUp方法中的静态号码

ReplevyControllerTest中补全初始化WebServletContext，并赋值所有参数。要求使用测试方法时ReplevyServiceImpl能取到WebServletContext中的值

在queryReplevyMainByReportNo方法里面改写返回类。
1. 返回类包含 List<ReplevyMainQueryDTO> 、 displayButton ：0-不显示 1-显示
2. 只有在resultList中flag全等于2时，displayButton = 1


1.ReplevyServiceImpl类中queryPlanAndDutyInfo方法调整返回类，要求新增DTO，只返回sql查询出来的字段，且返回json格式不变

1. 存主表之前需要先查询是否存在追偿案件
2. 明细表需要存 关联实收表clms_related_actual_receipt 
3. 追偿列表（明细） 费用列表 需要加删除。


在ReplevyServiceImpl类中写用于删除saveReplevyDetail、saveOrSubmitReplevyFee提交的数据方法。
1.要求删除是update表中状态字段valid_flag = N 无效。
2.要求写在一个方法里面，用接口字段initFlag区分
3.具体需求文档如下： 删除费用信息，点击费用列表中的删除按钮，调用deleteReplevyFee（报案号、赔案次数、费用id）方法，判断费用id不为空，根据id查询费用表clms_replevy_charge中的费用信息，根据该费用信息下的发票id和领款人id删除与费用表关联的发票表clms_invoice_info和领款人表clm_payment_info的数据，然后删除费用表中的数据。
删除追偿明细信息，点击追偿列表中的删除按钮，调用deleteReplevyDetail（追偿明细id）方法，判断追偿明细id不为空，根据追偿明细id删除追偿明细表clms_replevy_detail、追偿损失表clms_replevy_loss、关联实收表clms_related_actual_receipt。



主页面可以查到表A关联的表B列表，一对多的关系，B可以点击新增进入子页面，子页面点击暂存会存储B表数据。
怎么解决子页面点击暂存，重复新增一条数据。




子页面暂存数据时没有主键id，多次点击暂存生成了多条数据，怎么


在ReplevyServiceImpl写一个字段判断新增-1和修改-2的方法saveOrExamineReplevyText。
1. saveOrSubmitReplevy方法中追偿主页面提交调用saveOrExamineReplevyText，新增一条ClmsReplevyTextVo存值数据opinionType为1，其他字段也尽量赋值，通过clmsReplevyTextMapper类insert。
2. saveOrSubmitReplevyFee方法中追偿主页面提交调用saveOrExamineReplevyText，新增一条ClmsReplevyTextVo存值数据opinionType为2,其他字段也尽量赋值，通过clmsReplevyTextMapper类insert。
3. 提交追偿审批调用saveOrExamineReplevyText,通过opinionType为1和replevy_id去修改数据  。提交追偿费用审批时调用saveOrExamineReplevyText,通过opinionType为2和replevy_charge_id去修改数据  。




在ReplevyServiceImpl写一个查询方法

1.查询险种sql，要求用PlanPayMapper，查询条件REPORT_NO，查询结果放在ReplevyApiVo 的 List<PlanPayDTO>中：
SELECT  
(select PLAN_NAME from CLMS_policy_plan where CLMS_POLICY_PLAN.plan_code=CLM_PLAN_PAY.plan_code LIMIT 1)PLAN_NAME ,
PLAN_CODE
FROM CLM_PLAN_PAY pp,clm_case_base cb
where pp.CASE_NO = cb.CASE_NO and REPORT_NO = ''
group by PLAN_CODE ;

1.查询责任sql，要求用DutyPayMapper，查询条件REPORT_NO，查询结果放在ReplevyApiVo 的 List<DutyPayDTO>中：
select  
(select cpd.duty_name from CLMS_policy_duty cpd where cpd.duty_code=dp.duty_code limit 1) DUTY_NAME,
DUTY_code  
from clm_plan_duty_pay dp ,clm_case_base cb
where dp.CASE_NO = cb.CASE_NO and REPORT_NO = ''
group by DUTY_code  ;





在ReplevyServiceImpl中写一个查询方法
1.要求查询写在clmsReplevyMainMapper中，查询结果为集合，具体sql :
SELECT 
    m.report_no,
    m.replevy_no,
    row_number() over(partition by m.report_no order by m.case_times desc) SerialNo,
    m.sum_real_replevy,
    m.flag,
    (SELECT cp.PROCESS_STATUS FROM CLMS_case_process cp WHERE cp.report_no = m.report_no ORDER BY cp.case_times DESC LIMIT 1 ) AS PROCESS_STATUS,
    m.case_times
FROM clms_replevy_main m 
WHERE m.report_no = '91021000000001009763';
2.仿照initReplevyApprove查询方法，不要有过多的日志输出



在queryPlanAndDutyInfo改写"1".equals(initFlag)的查询sql如下：
SELECT
    dp.DUTY_CODE,
    (SELECT cpd.duty_name FROM CLMS_policy_duty cpd WHERE cpd.duty_code = dp.DUTY_CODE LIMIT 1) AS DUTY_NAME,
    pp.PLAN_CODE,
    (SELECT pl.PLAN_NAME FROM CLMS_policy_plan pl WHERE pl.PLAN_CODE = pp.PLAN_CODE LIMIT 1) AS PLAN_NAME,
    dp.DUTY_PAY_AMOUNT - COALESCE((SELECT SUM(r.replevied_money) 
                                   FROM clms_replevy_loss r 
                                   WHERE r.DUTY_CODE = dp.DUTY_CODE 
                                     AND r.PLAN_CODE = dp.PLAN_CODE 
                                     AND r.REPORT_NO = cb.REPORT_NO), 0) AS remaining_amount
FROM CLM_PLAN_PAY pp
JOIN clm_plan_duty_pay dp ON pp.PLAN_CODE = dp.PLAN_CODE
JOIN clm_case_base cb ON pp.CASE_NO = cb.CASE_NO
WHERE cb.REPORT_NO = '98081000000001002995'
  AND pp.PLAN_PAY_AMOUNT > 0
  AND dp.DUTY_PAY_AMOUNT > 0
GROUP BY dp.DUTY_CODE, pp.PLAN_CODE;

要求：
查询出来一组PLAN_CODE、PLAN_NAME会对应多个DUTY_CODE、DUTY_NAME、remaining_amount ，需要将返回数据归类PLAN_CODE、PLAN_NAME为一个listA ，然后每个listA 嵌套多DUTY_CODE、DUTY_NAME、remaining_amount


在saveOrUpdateReplevyLoss方法中 clmsReplevyLossMapper.insertSelective(replevyLossPo)记录数据之前需要校验，通过DUTY_CODE、PLAN_CODE分组，每组的历史replevied_money总和加本次的金额不能大于历史DUTY_PAY_AMOUNT总和
1. clmsReplevyLossMapper.java 、clmsReplevyLossMapper.xml中写sql查询,历史replevied_money总和查询sql
SELECT
    dp.DUTY_CODE,
    dp.PLAN_CODE,
    sum(dp.replevied_money)
FROM clms_replevy_loss dp
where dp.replevy_no  = '48082507100002007230' 
GROUP BY dp.DUTY_CODE, dp.PLAN_CODE;
2. DutyPayMapper.java 、DutyPayMapper.xml中写sql查询,历史DUTY_PAY_AMOUNT总和查询sql
SELECT
    dp.DUTY_CODE,
    dp.PLAN_CODE,
    sum(dp.DUTY_PAY_AMOUNT)
FROM clm_plan_duty_pay dp
where dp.CASE_NO  = '48082507100002007230' 
AND dp.DUTY_PAY_AMOUNT > 0
GROUP BY dp.DUTY_CODE, dp.PLAN_CODE;



同一组DUTY_CODE、PLAN_CODE replevied_money





1.在方法checkReplevySubmit中写校验规则。
要求校验Clms_Replevy_Detail表中必须有一条数据，用报案号和追偿案件号查询，否则报错“请录入追偿收入明细”。
且Clms_Replevy_Detail表中数据必须都是已提交状态Flag=1。否则报错“追偿收入明细存在必录项未录入”。

2.在replevyDetailCheckSubmit方法中校验。
校验“核销金额总和需要等于实际追回金额总和”，clms_replevy_loss表中replevied_money字段总和等于clms_related_actual_receipt表中write_off_amount


    所有有数据 <= 0 或 有数据 <= 0部分数据为NULL/空值 返回false
 
改造sql ,只有当查出来所有TOTAL_VALUE_ADDED_TAX都<= 0 或 有数据 TOTAL_VALUE_ADDED_TAX<= 0部分数据为NULL/空值 返回false，其他情况返回true, 查不到数据 或者 TOTAL_VALUE_ADDED_TAX值全为NULL 或空值 返回true,
SELECT
			CASE
				WHEN NOT EXISTS (
					SELECT 1
					FROM ply_duty d
							 JOIN PLY_PLAN p ON d.ID_PLY_PLAN = p.ID_PLY_PLAN
					WHERE d.duty_code = #{dutyCode,jdbcType=VARCHAR}
					  AND p.plan_code = #{planCode,jdbcType=VARCHAR}
					  AND d.policy_No = #{policyNo,jdbcType=VARCHAR}
					  AND COALESCE(d.TOTAL_VALUE_ADDED_TAX, 0) > 0
				)
					AND EXISTS (
						SELECT 1
						FROM ply_duty d
								 JOIN PLY_PLAN p ON d.ID_PLY_PLAN = p.ID_PLY_PLAN
						WHERE d.duty_code = #{dutyCode,jdbcType=VARCHAR}
						  AND p.plan_code = #{planCode,jdbcType=VARCHAR}
						  AND d.policy_No = #{policyNo,jdbcType=VARCHAR}
					)
					THEN 'true'
				ELSE 'false'
				END AS result   ;
				
				
				
				
			
				
				原sql如下：
				 SELECT
            pp.plan_code,
            (SELECT pl.PLAN_NAME FROM CLMS_policy_plan pl WHERE pl.PLAN_CODE = pp.PLAN_CODE LIMIT 1) AS PLAN_NAME,
            dp.duty_code,
            (SELECT cpd.duty_name FROM CLMS_policy_duty cpd WHERE cpd.duty_code = dp.DUTY_CODE LIMIT 1) AS DUTY_NAME,
            sum(dp.DUTY_PAY_AMOUNT) as dutyPayAmount,
            sum(dp.DUTY_PAY_AMOUNT) - COALESCE((SELECT SUM(r.replevied_money) FROM clms_replevy_loss r
                                                WHERE r.DUTY_CODE = dp.DUTY_CODE
                                                  AND r.PLAN_CODE = pp.PLAN_CODE
                                                  AND r.valid_flag='Y'
                                                  AND r.REPORT_NO =  #{reportNo}), 0) AS remaining_amount
        FROM clms_payment_plan pp
            JOIN clms_payment_duty dp ON pp.ID_CLMS_PAYMENT_PLAN = dp.ID_CLMS_PAYMENT_PLAN
        WHERE pp.ID_CLM_PAYMENT_ITEM IN (SELECT ID_CLM_PAYMENT_ITEM FROM id_list) and dp.DUTY_PAY_AMOUNT > 0
        GROUP BY pp.plan_code, dp.duty_code;
				
		需求将查询PLAN_NAME、DUTY_NAME语句改造成如下sql： select c.duty_name
        from clms_policy_info a,clms_policy_plan b,clms_policy_duty c
        where a.ID_AHCS_POLICY_INFO=b.ID_AHCS_POLICY_INFO
          and b.ID_AHCS_POLICY_PLAN=c.ID_AHCS_POLICY_PLAN
          and b.plan_code=#{planCode}
          and c.duty_code=#{dutyCode}
          and REPORT_NO=#{reportNo}
        limit 1
				
				
				

仿照下面 2.6.5 页面要素 的字段要素和已有的 InvestigateAdditionalDTO、InvestigateAssistDTO、InvestigateAuditDTO、InvestigateDTO、InvestigateEvaluateDTO、InvestigateProcessDTO、InvestigateScoreDTO、InvestigateTaskAuditDTO、InvestigateTaskDTO、InvestigatorDto、OuterInvestigateResultDTO 表，用entrustment替换investigate创建一套第三方委托的表格，需要写出建表sql。

仿照 InvestigateController、InvestigateProcessController、InvestigateTaskAuditController 、InvestigateTaskController 中的方法，在EntrustmentController写出第三方委托功能。
仿照 InvestigateAuditServiceImpl、InvestigateProcessServiceImpl、InvestigateScoreServiceImpl、InvestigateServiceImpl、InvestigateTaskAuditServiceImpl、InvestigateTaskServiceImpl、OuterInvestigateServiceImpl 在EntrustmentService中写出实现方法。

具体需求功能如下：
第三方委托功能 
2.6.1	业务背景
当前理赔系统无第三方委托功能，现增加此功能。
2.6.2	操作入口
点击页面底部按钮“提调/第三方委托”进入第三方委托页面。
工作台，第三方委托审批任务点击“处理”进入第三方委托审批页面。
2.6.3	功能描述
第三方委托流程：1.第三方委托申请 2.第三方委托审批 3.结束并允许打印
调整提调时“事故场景”码值；
增加第三方委托发起、审批、查看功能：1. 发起委托仿照 getHistoryInvestigateByReportNo 查询历史数据 2. 发起审批仿照 getInvestigateById查询委托信息、 getHistoryInvestigateByReportNo 查询历史委托数据、getInvestigateUserListByDepartment 查询用户列表信息 3.查看与审批查询数据一致
若第三方委托类型为“公估”时，需打印委托书: 1.仿照InvestigateController类中的getHistoryOutInvestigateByReportNo查询方法 2.沿用PrintController 的commissionPrintResult打印下载接口。

1.在initEntrustment中实现当为提交操作时，生成一笔审批任务，用entrustmentAuditMapper进行sql
2.字段数据复制entrustmentApiVo.getEntrustmentDTO()中的数据。其中policy_no、insured_name通过已有sql查询clms_insured_person和clms_policy_info表，initiator_um、initiator_um_name取UserInfoDTO中数据。
3.在EntrustmentController中getEntrustmentData实现历史审批信息查询，要求通过本次委托id查询出所有关联的审批记录List<EntrustmentAuditDTO> 。
4.在EntrustmentController中新增一个提交审批方法，并在EntrustmentService和EntrustmentServiceImpl中实现此方法，要求更新clms_entrustment、clms_entrustment_audit中状态。若审批不同意，则审批提交后，需生成一条提醒任务，提醒任务在NoticeServiceImpl.java类公共方法saveNotices里，提醒内容：“{报案号}第三方委托申请被{审批人}审批退回”。

在replevyCheckSubmit方法中"3".equals(flag)时，写一个查询【关联责任非免税责任】的sql， 写在 OcasMapper中  sql为：
SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1
            FROM ply_duty d
            JOIN PLY_PLAN p ON d.ID_PLY_PLAN = p.ID_PLY_PLAN
            WHERE d.duty_code = ''
              AND p.plan_code = ''
              AND d.policyNo = ''
              AND d.TOTAL_VALUE_ADDED_TAX > 0
        ) THEN 'true'
        ELSE 'false'
    END AS result;

(1)	


方法deleteReplevyFeeInternal中updateTotalRepleviedMoney里面的clmsReplevyChargeMapper.getTotalChargeMoney查询方法执行前，  clmsReplevyChargeMapper.updateSelectiveByPrimaryKey中的updatesql并未提交或执行，导致clmsReplevyChargeMapper.getTotalChargeMoney查询导致提交前数据。
要求修改代码，保证clmsReplevyChargeMapper.getTotalChargeMoney查询在clmsReplevyChargeMapper.updateSelectiveByPrimaryKey执行完成之后执行。


1.在deleteReplevyFeeInternal中实现更新clms_replevy_main中金额字段sum_replevy_fee, 需要用现有金额减去replevyCharge.getChargeMoney()，并更新数据库。

2.在deleteReplevyDetailInternal中实现更新clms_replevy_main中金额字段sum_real_replevy, 需要用现有金额减去sumRepleviedMoney，并更新数据库。

checkReplevy中实现通过clmsReplevyMain的字段Flag（0-追偿处理中 1-追偿待审核 2-追偿已完成），以及登录人和处理人对比判断：
当“追偿处理中”登录人(UserInfoDTO userInfoDTO = WebServletContext.getUser(); userInfoDTO.getUserCode();)和处理人(queryReplevyMainByReportNo方法查询ASSIGNER字段)不一致，返回false 。“追偿待审核”返回false。其他情况返回true。




1.按照create_entrustment_tables.sql中的clms_entrustment表字段改写EntrustmentDTO中的字段参数、EntrustmentMapper.xml中字段参数

2.按照create_entrustment_tables.sql中的clms_entrustment_audit表字段分别改写EntrustmentAuditDTO中的字段参数、改写 EntrustmentAuditMapper.xml文件


1.按照create_entrustment_tables.sql中的clms_entrustment表新增的valid_flag字段 ，在EntrustmentDTO中的字段参数新增、EntrustmentMapper.xml中字段参数新增

2.按照create_entrustment_tables.sql中的clms_entrustment_audit表新增的valid_flag字段，在EntrustmentAuditDTO中的字段参数新增、EntrustmentAuditMapper.xml中字段参数新增



需求：第三方类型为“公估”的委托，审批同意后，可在“打印中心-公估委托书”菜单打印委托书 
查询clms_entrustment表，展示list

扩展PrintController.java中的commissionPrintResult方法，以及调用的printCoreService.saveCommissionFileAsync方法，需要通过传参PrintEntrustDTO中的printFlag字段区分1-提调 2-委托，并改造非公共方法
1.需要加强代码扩展性，不要有冗余代码，不要新建方法在共用的方法里面通过printFlag区分1-提调 2-委托的代码。
2.PrintController中获取fileId 需要区分1-提调 2-委托后分别取，2-委托取EntrustmentDTO中的fileId。
3.PrintCoreServiceImpl中的saveCommissionFile方法中原来使用1-提调的主键IdAhcsInvestigate的地方，都通过printFlag区分后使用 2-委托的主键idEntrustment。
4.bulidPrintInfo方法也区分1-提调 2-委托。
5.bulidAppraisalCommissionDTO 组装数据也使用原有方法，通过printFlag区分1-提调 2-委托的代码，委托公估公司信息部分数据取EntrustmentDTO中字段。
6.需要新增的查询sql都仿照提调写


1.需要加强代码扩展性，不要有冗余代码，不要新建方法在共用的方法里面通过printFlag区分1-提调 2-委托的代码。


写一个测试类
1.测试EntrustmentController中的方法
2.测试PrintController中的commissionPrintResult方法， 分别测试提调、委托生产文件